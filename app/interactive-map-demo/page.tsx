'use client';

import React from 'react';
import InteractiveMap from '../../components/InteractiveMap';
import { Location, Alert, Route } from '../../types/map';

export default function InteractiveMapDemo() {
  // Example of custom data that can be passed to the map
  const customLocations: Location[] = [
    {
      id: 1,
      name: 'Custom Location 1',
      nameAr: 'موقع مخصص 1',
      lat: 24.7136,
      lng: 46.6753,
      type: 'airport',
      description: 'Custom airport location',
      descriptionAr: 'موقع مطار مخصص',
      status: 'active',
      facilities: ['customs', 'cargo']
    },
    {
      id: 2,
      name: 'Custom Location 2',
      nameAr: 'موقع مخصص 2',
      lat: 21.4858,
      lng: 39.1925,
      type: 'seaport',
      description: 'Custom seaport location',
      descriptionAr: 'موقع ميناء مخصص',
      status: 'active',
      facilities: ['customs', 'container']
    }
  ];

  // Event handlers
  const handleLocationClick = (location: Location) => {
    console.log('Location clicked:', location);
    // You can implement custom logic here, such as:
    // - Show detailed information modal
    // - Navigate to location details page
    // - Update other components
  };

  const handleAlertClick = (alert: Alert) => {
    console.log('Alert clicked:', alert);
    // You can implement custom logic here, such as:
    // - Mark alert as read
    // - Show alert details modal
    // - Navigate to alert management page
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log('Routes selected:', routes);
    // You can implement custom logic here, such as:
    // - Display routes on map
    // - Calculate route statistics
    // - Save user preferences
  };

  return (
    <div className="w-full h-screen">
      <InteractiveMap
        // Optional: Pass custom data
        // locations={customLocations}
        // alerts={customAlerts}
        // routes={customRoutes}
        // statistics={customStatistics}
        
        // Map configuration
        initialCenter={{ lat: 24.7136, lng: 46.6753 }}
        initialZoom={6}
        showSidebar={true}
        language="en" // or "ar" for Arabic
        
        // Event handlers
        onLocationClick={handleLocationClick}
        onAlertClick={handleAlertClick}
        onRouteSelect={handleRouteSelect}
      />
    </div>
  );
}

// Example of how to use the component with different configurations:

// 1. Basic usage with default data:
// <InteractiveMap />

// 2. Arabic language with custom center:
// <InteractiveMap 
//   language="ar"
//   initialCenter={{ lat: 21.4858, lng: 39.1925 }}
//   initialZoom={8}
// />

// 3. Without sidebar:
// <InteractiveMap showSidebar={false} />

// 4. With custom data:
// <InteractiveMap 
//   locations={myCustomLocations}
//   alerts={myCustomAlerts}
//   routes={myCustomRoutes}
//   statistics={myCustomStatistics}
// />

// 5. With event handlers:
// <InteractiveMap 
//   onLocationClick={(location) => {
//     // Handle location click
//     showLocationDetails(location);
//   }}
//   onAlertClick={(alert) => {
//     // Handle alert click
//     markAlertAsRead(alert.id);
//   }}
//   onRouteSelect={(routes) => {
//     // Handle route selection
//     displayRoutesOnMap(routes);
//   }}
// />
