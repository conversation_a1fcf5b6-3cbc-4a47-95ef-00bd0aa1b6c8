"use client";

import React from "react";
import { InteractiveMap } from "../../../components";
import { Location, Alert, Route } from "../../../types/map";

export default function AhmedTestPage() {
  // Event handlers for testing
  const handleLocationClick = (location: Location) => {
    console.log("Location clicked:", location);
    // You can add custom logic here for testing
  };

  const handleAlertClick = (alert: Alert) => {
    console.log("Alert clicked:", alert);
    // Mark alert as read or show details
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log("Routes selected:", routes);
    // Process selected routes
  };

  return (
    <div className="w-full h-screen">
      {/* Interactive Map Component */}
      <InteractiveMap
        // Map configuration
        initialCenter={{ lat: 24.7136, lng: 46.6753 }} // Riyadh center
        initialZoom={6}
        showSidebar={true}
        // Language will be auto-detected from site settings
        // No need to specify language prop unless you want to override

        // Event handlers
        onLocationClick={handleLocationClick}
        onAlertClick={handleAlertClick}
        onRouteSelect={handleRouteSelect}
      />
    </div>
  );
}
