'use client';

import React, { useState } from 'react';
import InteractiveMap from '../../components/InteractiveMap';
import { Location, Alert, Route, TripStatistics } from '../../types/map';

export default function CustomMapExample() {
  const [language, setLanguage] = useState<'en' | 'ar'>('en');

  // Custom locations for a specific region (Western Saudi Arabia)
  const customLocations: Location[] = [
    {
      id: 1,
      name: 'Jeddah Islamic Seaport',
      nameAr: 'ميناء جدة الإسلامي',
      lat: 21.4858,
      lng: 39.1925,
      type: 'seaport',
      description: 'Major commercial seaport in Jeddah',
      descriptionAr: 'ميناء تجاري رئيسي في جدة',
      status: 'active',
      facilities: ['customs', 'cargo', 'container']
    },
    {
      id: 2,
      name: 'King Abdulaziz International Airport',
      nameAr: 'مطار الملك عبدالعزيز الدولي',
      lat: 21.6796,
      lng: 39.1564,
      type: 'airport',
      description: 'International airport serving Jeddah',
      descriptionAr: 'مطار دولي يخدم جدة',
      status: 'active',
      facilities: ['customs', 'cargo', 'passenger']
    },
    {
      id: 3,
      name: 'Makkah Customs',
      nameAr: 'جمارك مكة',
      lat: 21.3891,
      lng: 39.8579,
      type: 'checkpoint',
      description: 'Customs checkpoint in Makkah',
      descriptionAr: 'نقطة جمركية في مكة',
      status: 'active',
      facilities: ['customs', 'immigration']
    }
  ];

  // Custom alerts for the region
  const customAlerts: Alert[] = [
    {
      id: 1,
      title: 'High Traffic Alert',
      titleAr: 'تنبيه الازدحام المروري',
      description: 'Heavy traffic detected on Jeddah-Makkah highway',
      descriptionAr: 'ازدحام مروري كثيف على طريق جدة-مكة السريع',
      time: '5 mins ago',
      timeAr: 'منذ 5 دقائق',
      severity: 'medium',
      type: 'delay',
      vehicleId: 'VH101',
      location: {
        lat: 21.5,
        lng: 39.5,
        address: 'Jeddah-Makkah Highway'
      },
      isRead: false,
      timestamp: new Date().toISOString()
    },
    {
      id: 2,
      title: 'Container Security Alert',
      titleAr: 'تنبيه أمان الحاوية',
      description: 'Unauthorized access attempt detected',
      descriptionAr: 'تم اكتشاف محاولة وصول غير مصرح بها',
      time: '15 mins ago',
      timeAr: 'منذ 15 دقيقة',
      severity: 'high',
      type: 'elock',
      vehicleId: 'VH102',
      containerId: 'C201',
      location: {
        lat: 21.4858,
        lng: 39.1925,
        address: 'Jeddah Islamic Seaport'
      },
      isRead: false,
      timestamp: new Date().toISOString()
    }
  ];

  // Custom routes for the region
  const customRoutes: Route[] = [
    {
      id: 1,
      name: 'Jeddah Port to Makkah',
      nameAr: 'ميناء جدة إلى مكة',
      startLocation: {
        id: 1,
        name: 'Jeddah Islamic Seaport',
        lat: 21.4858,
        lng: 39.1925
      },
      endLocation: {
        id: 3,
        name: 'Makkah Customs',
        lat: 21.3891,
        lng: 39.8579
      },
      distance: 85,
      estimatedDuration: 60,
      status: 'active',
      type: 'commercial',
      waypoints: [],
      isUserRoute: true
    },
    {
      id: 2,
      name: 'Airport to Seaport',
      nameAr: 'المطار إلى الميناء',
      startLocation: {
        id: 2,
        name: 'King Abdulaziz International Airport',
        lat: 21.6796,
        lng: 39.1564
      },
      endLocation: {
        id: 1,
        name: 'Jeddah Islamic Seaport',
        lat: 21.4858,
        lng: 39.1925
      },
      distance: 25,
      estimatedDuration: 30,
      status: 'active',
      type: 'transit',
      waypoints: [],
      isUserRoute: true
    }
  ];

  // Custom statistics
  const customStatistics: TripStatistics = {
    pieData: [
      {
        name: 'Active Trips With Alerts',
        nameAr: 'الرحلات النشطة مع التنبيهات',
        value: 5,
        color: '#ef4444',
        percentage: 12.5
      },
      {
        name: 'Active Trips Without Alerts',
        nameAr: 'الرحلات النشطة بدون تنبيهات',
        value: 35,
        color: '#22c55e',
        percentage: 87.5
      }
    ],
    barData: [
      {
        name: 'Western Region',
        nameAr: 'المنطقة الغربية',
        activeTrips: 40,
        activeTripsWithAlerts: 5,
        activeTripsWithCommunicationLost: 0,
        completedTrips: 120,
        delayedTrips: 3,
        onTimeTrips: 117
      }
    ],
    summary: {
      totalActiveTrips: 40,
      totalCompletedTrips: 120,
      totalAlerts: 5,
      communicationLostTrips: 0,
      averageDelay: 8,
      onTimePercentage: 97.5,
      lastUpdated: new Date().toISOString()
    }
  };

  const handleLocationClick = (location: Location) => {
    alert(`Location clicked: ${language === 'ar' ? location.nameAr : location.name}`);
  };

  const handleAlertClick = (alert: Alert) => {
    alert(`Alert clicked: ${language === 'ar' ? alert.titleAr : alert.title}`);
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log('Selected routes:', routes);
    alert(`Selected ${routes.length} route(s)`);
  };

  return (
    <div className="w-full h-screen relative">
      {/* Language Toggle */}
      <div className="absolute top-4 left-4 z-[1002] bg-white rounded-lg shadow-md border p-2">
        <button
          onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
        >
          {language === 'en' ? 'العربية' : 'English'}
        </button>
      </div>

      {/* Interactive Map with Custom Data */}
      <InteractiveMap
        locations={customLocations}
        alerts={customAlerts}
        routes={customRoutes}
        statistics={customStatistics}
        initialCenter={{ lat: 21.5, lng: 39.5 }} // Center on Western Saudi Arabia
        initialZoom={8}
        showSidebar={true}
        language={language}
        onLocationClick={handleLocationClick}
        onAlertClick={handleAlertClick}
        onRouteSelect={handleRouteSelect}
      />
    </div>
  );
}
