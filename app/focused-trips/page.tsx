'use client';

import React from 'react';
import { InteractiveMap } from '../../components';
import { Location, Alert, Route } from '../../types/map';

export default function FocusedTripsPage() {
  // Event handlers for focused trips
  const handleLocationClick = (location: Location) => {
    console.log('Focused Trips - Location clicked:', location);
    // Add priority location handling
  };

  const handleAlertClick = (alert: Alert) => {
    console.log('Focused Trips - Alert clicked:', alert);
    // Add priority alert handling
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log('Focused Trips - Routes selected:', routes);
    // Add priority route handling
  };

  return (
    <div className="w-full h-screen">
      <InteractiveMap
        pageType="focused-trips"
        initialCenter={{ lat: 24.7136, lng: 46.6753 }}
        initialZoom={6}
        showSidebar={true}
        onLocationClick={handleLocationClick}
        onAlertClick={handleAlertClick}
        onRouteSelect={handleRouteSelect}
      />
    </div>
  );
}
