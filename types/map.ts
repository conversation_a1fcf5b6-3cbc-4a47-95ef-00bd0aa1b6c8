// Location and Map Types
export interface Location {
  id: number;
  name: string;
  nameAr: string;
  lat: number;
  lng: number;
  type: LocationType;
  description?: string;
  descriptionAr?: string;
  status: "active" | "inactive";
  facilities?: string[];
}

export type LocationType =
  | "airport"
  | "seaport"
  | "landport"
  | "checkpoint"
  | "police_station";

export interface MapMarkerIcon {
  url: string;
  scaledSize: google.maps.Size;
  anchor: google.maps.Point;
}

export interface MapCenter {
  lat: number;
  lng: number;
}

export interface MapConfig {
  center: MapCenter;
  zoom: number;
  mapTypeId?: google.maps.MapTypeId;
}

// Alert Types
export interface Alert {
  id: number;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  time: string;
  timeAr: string;
  severity: AlertSeverity;
  type: AlertType;
  vehicleId: string;
  containerId?: string;
  location: AlertLocation;
  isRead: boolean;
  timestamp: string;
  expectedTime?: string;
  actualTime?: string;
  stopDuration?: number;
  lastKnownLocation?: AlertLocation;
}

export type AlertSeverity = "high" | "medium" | "low";

export type AlertType =
  | "speed"
  | "geofence"
  | "elock"
  | "delay"
  | "stop"
  | "communication";

export interface AlertLocation {
  lat: number;
  lng: number;
  address: string;
}

// Route Types
export interface Route {
  id: number;
  name: string;
  nameAr: string;
  startLocation: RouteLocation;
  endLocation: RouteLocation;
  distance: number; // in kilometers
  estimatedDuration: number; // in minutes
  status: "active" | "inactive";
  type: RouteType;
  waypoints: Waypoint[];
  isUserRoute: boolean;
}

export type RouteType =
  | "commercial"
  | "transit"
  | "export"
  | "import"
  | "domestic";

export interface RouteLocation {
  id: number;
  name: string;
  lat: number;
  lng: number;
}

export interface Waypoint {
  lat: number;
  lng: number;
  name: string;
}

// Statistics Types
export interface PieChartData {
  name: string;
  nameAr: string;
  value: number;
  color: string;
  percentage: number;
}

export interface BarChartData {
  name: string;
  nameAr: string;
  activeTrips: number;
  activeTripsWithAlerts: number;
  activeTripsWithCommunicationLost: number;
  completedTrips: number;
  delayedTrips: number;
  onTimeTrips: number;
}

export interface TripStatistics {
  pieData: PieChartData[];
  barData: BarChartData[];
  summary: {
    totalActiveTrips: number;
    totalCompletedTrips: number;
    totalAlerts: number;
    communicationLostTrips: number;
    averageDelay: number;
    onTimePercentage: number;
    lastUpdated: string;
  };
}

export interface VehicleStatistics {
  totalVehicles: number;
  activeVehicles: number;
  inactiveVehicles: number;
  maintenanceVehicles: number;
  vehicleTypes: VehicleType[];
}

export interface VehicleType {
  type: string;
  typeAr: string;
  count: number;
  active: number;
}

export interface RouteStatistics {
  totalRoutes: number;
  activeRoutes: number;
  userRoutes: number;
  mostUsedRoutes: MostUsedRoute[];
}

export interface MostUsedRoute {
  routeId: number;
  name: string;
  usageCount: number;
  averageTime: number;
}

export interface AlertStatistics {
  totalAlerts: number;
  unreadAlerts: number;
  highSeverityAlerts: number;
  mediumSeverityAlerts: number;
  lowSeverityAlerts: number;
  alertTypes: AlertTypeStatistic[];
}

export interface AlertTypeStatistic {
  type: string;
  typeAr: string;
  count: number;
  percentage: number;
}

// Filter Types
export interface SearchFilters {
  showPort: boolean;
  showCheckpost: boolean;
  tripCode: string;
  searchValue: string;
}

export interface RouteFilters {
  filter: "my-routes" | "all";
  selectedRoutes: string[];
  searchTerm: string;
}

// Sidebar Types
export type SidebarTab = "search" | "alerts" | "routes";

export interface SidebarState {
  isOpen: boolean;
  activeTab: SidebarTab;
}

// Component Props Types
export interface InteractiveMapProps {
  locations?: Location[];
  alerts?: Alert[];
  routes?: Route[];
  statistics?: TripStatistics;
  initialCenter?: MapCenter;
  initialZoom?: number;
  showSidebar?: boolean;
  language?: "en" | "ar"; // If not provided, will auto-detect from site settings
  onLocationClick?: (location: Location) => void;
  onAlertClick?: (alert: Alert) => void;
  onRouteSelect?: (routes: Route[]) => void;
}

export interface MapComponentProps {
  locations: Location[];
  center: MapCenter;
  zoom: number;
  onLocationClick?: (location: Location) => void;
}

export interface SidebarProps {
  isOpen: boolean;
  activeTab: SidebarTab;
  onTabChange: (tab: SidebarTab) => void;
  onClose: () => void;
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
  language?: "en" | "ar";
  onAlertClick?: (alert: Alert) => void;
  onRouteSelect?: (routes: Route[]) => void;
}

// Hook Types
export interface UseMapData {
  locations: Location[];
  alerts: Alert[];
  routes: Route[];
  statistics: TripStatistics;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseMapState {
  map: google.maps.Map | null;
  markers: google.maps.Marker[];
  isLoaded: boolean;
  center: MapCenter;
  zoom: number;
  setCenter: (center: MapCenter) => void;
  setZoom: (zoom: number) => void;
}

export interface UseSidebarState {
  isOpen: boolean;
  activeTab: SidebarTab;
  searchFilters: SearchFilters;
  routeFilters: RouteFilters;
  setIsOpen: (isOpen: boolean) => void;
  setActiveTab: (tab: SidebarTab) => void;
  updateSearchFilters: (filters: Partial<SearchFilters>) => void;
  updateRouteFilters: (filters: Partial<RouteFilters>) => void;
  resetFilters: () => void;
}
