import { useState, useEffect } from 'react';
import { Location, Alert, Route, TripStatistics, UseMapData } from '../types/map';

// Import JSON data
import locationsData from '../data/locations.json';
import alertsData from '../data/alerts.json';
import routesData from '../data/routes.json';
import statisticsData from '../data/statistics.json';

export const useMapData = (): UseMapData => {
  const [locations, setLocations] = useState<Location[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [routes, setRoutes] = useState<Route[]>([]);
  const [statistics, setStatistics] = useState<TripStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load data from JSON files
      setLocations(locationsData.locations);
      setAlerts(alertsData.alerts);
      setRoutes(routesData.routes);
      setStatistics(statisticsData.tripStatistics);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      console.error('Error loading map data:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    await loadData();
  };

  useEffect(() => {
    loadData();
  }, []);

  return {
    locations,
    alerts,
    routes,
    statistics: statistics || {
      pieData: [],
      barData: [],
      summary: {
        totalActiveTrips: 0,
        totalCompletedTrips: 0,
        totalAlerts: 0,
        communicationLostTrips: 0,
        averageDelay: 0,
        onTimePercentage: 0,
        lastUpdated: new Date().toISOString()
      }
    },
    loading,
    error,
    refetch
  };
};

// Hook for filtering locations based on search criteria
export const useFilteredLocations = (
  locations: Location[],
  showPort: boolean,
  showCheckpost: boolean,
  searchValue: string
) => {
  return useState(() => {
    return locations.filter(location => {
      // Filter by type
      const typeFilter = 
        (showPort && ['airport', 'seaport', 'landport'].includes(location.type)) ||
        (showCheckpost && ['checkpoint', 'police_station'].includes(location.type));

      if (!typeFilter) return false;

      // Filter by search value
      if (searchValue.trim()) {
        const searchLower = searchValue.toLowerCase();
        return (
          location.name.toLowerCase().includes(searchLower) ||
          location.nameAr.includes(searchValue) ||
          location.description?.toLowerCase().includes(searchLower) ||
          location.descriptionAr?.includes(searchValue)
        );
      }

      return true;
    });
  })[0];
};

// Hook for filtering alerts
export const useFilteredAlerts = (
  alerts: Alert[],
  severity?: string,
  type?: string,
  isRead?: boolean
) => {
  return useState(() => {
    return alerts.filter(alert => {
      if (severity && alert.severity !== severity) return false;
      if (type && alert.type !== type) return false;
      if (isRead !== undefined && alert.isRead !== isRead) return false;
      return true;
    });
  })[0];
};

// Hook for filtering routes
export const useFilteredRoutes = (
  routes: Route[],
  filter: 'my-routes' | 'all',
  searchTerm: string
) => {
  return useState(() => {
    let filteredRoutes = routes;

    // Filter by user routes
    if (filter === 'my-routes') {
      filteredRoutes = routes.filter(route => route.isUserRoute);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filteredRoutes = filteredRoutes.filter(route =>
        route.name.toLowerCase().includes(searchLower) ||
        route.nameAr.includes(searchTerm)
      );
    }

    return filteredRoutes;
  })[0];
};
