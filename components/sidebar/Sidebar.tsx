'use client';

import React from 'react';
import { SidebarProps } from '../../types/map';
import SidebarTabs from './SidebarTabs';
import SearchTab from './SearchTab';
import AlertsTab from './AlertsTab';
import RoutesTab from './RoutesTab';

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  activeTab,
  onTabChange,
  onClose,
  alerts,
  routes,
  statistics,
  language = 'en',
  onAlertClick,
  onRouteSelect
}) => {
  if (!isOpen) return null;

  const isRTL = language === 'ar';

  return (
    <div
      className={`fixed bg-white shadow-xl border-l w-96 overflow-y-auto z-[999] ${
        isRTL ? 'left-0 border-r border-l-0' : 'right-0'
      }`}
      style={{
        top: '112px',
        bottom: '40px'
      }}
    >
      <div className="h-full flex flex-col relative">
        {/* Tab Navigation */}
        <SidebarTabs
          activeTab={activeTab}
          onTabChange={onTabChange}
          language={language}
        />

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'search' && (
            <SearchTab
              statistics={statistics}
              language={language}
            />
          )}

          {activeTab === 'alerts' && (
            <AlertsTab
              alerts={alerts}
              language={language}
              onAlertClick={onAlertClick}
            />
          )}

          {activeTab === 'routes' && (
            <RoutesTab
              routes={routes}
              language={language}
              onRouteSelect={onRouteSelect}
            />
          )}
        </div>

        {/* Close Button */}
        <div className="border-t bg-white">
          <button
            onClick={onClose}
            className="w-full bg-red-500 text-white px-4 py-3 hover:bg-red-600 transition-colors font-medium"
          >
            {language === 'ar' ? 'إغلاق الشريط الجانبي' : 'Close Sidebar'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
