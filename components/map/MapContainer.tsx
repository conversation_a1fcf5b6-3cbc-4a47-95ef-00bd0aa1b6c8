'use client';

import React from 'react';
import { MapComponentProps } from '../../types/map';
import { useMapState, useMapMarkers } from '../../hooks/useMapState';
import LoadingSpinner from './LoadingSpinner';

const MapContainer: React.FC<MapComponentProps> = ({
  locations,
  center,
  zoom,
  onLocationClick
}) => {
  const { map, isLoaded, mapRef } = useMapState(center, zoom);
  
  // Create and manage markers
  useMapMarkers(map, locations, onLocationClick);

  if (!isLoaded) {
    return <LoadingSpinner />;
  }

  return (
    <div
      ref={mapRef}
      className="absolute inset-0 w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default MapContainer;
