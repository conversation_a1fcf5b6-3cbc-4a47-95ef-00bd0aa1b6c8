/**
 * @fileoverview Interactive Map Container Component
 * @description المكون الرئيسي للخريطة التفاعلية الذي يجمع جميع المكونات الفرعية
 * ويدير البيانات والحالة العامة للخريطة
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-15
 *
 * @features
 * - عرض خريطة Google Maps تفاعلية
 * - شريط جانبي مع تبويبات متعددة (البحث، التنبيهات، الطرق)
 * - دعم أنواع بيانات مختلفة حسب نوع الصفحة
 * - اكتشاف اللغة التلقائي من إعدادات الموقع
 * - أزرار تحكم للخريطة (إعادة تعيين، ملء الشاشة)
 * - رسوم بيانية لعرض الإحصائيات
 *
 * @usage
 * ```tsx
 * // استخدام أساسي
 * <InteractiveMapContainer />
 *
 * // مع نوع بيانات محدد
 * <InteractiveMapContainer pageType="location-monitor" />
 *
 * // مع بيانات مخصصة
 * <InteractiveMapContainer
 *   locations={customLocations}
 *   alerts={customAlerts}
 * />
 * ```
 */

"use client";

import React, { useMemo } from "react";
import { InteractiveMapProps } from "../../types/map";
import { usePageData } from "../../hooks/usePageData";
import { useSidebarState } from "../../hooks/useSidebarState";
import { useLanguage } from "../../hooks/useLanguage";
import GoogleMapView from "./GoogleMapView";
import MapControlButtons from "./MapControlButtons";
import MapSidebar from "./MapSidebar";
import MapLoadingScreen from "./MapLoadingScreen";

/**
 * Interactive Map Container Component
 *
 * @description المكون الرئيسي للخريطة التفاعلية الذي يدير جميع المكونات الفرعية
 * ويتعامل مع تحميل البيانات وإدارة الحالة
 *
 * @param {InteractiveMapProps} props - خصائص المكون
 * @param {Location[]} [props.locations] - مصفوفة المواقع المخصصة (اختيارية)
 * @param {Alert[]} [props.alerts] - مصفوفة التنبيهات المخصصة (اختيارية)
 * @param {Route[]} [props.routes] - مصفوفة الطرق المخصصة (اختيارية)
 * @param {TripStatistics} [props.statistics] - إحصائيات مخصصة (اختيارية)
 * @param {PageType} [props.pageType='default'] - نوع البيانات المطلوب تحميلها
 * @param {MapCenter} [props.initialCenter] - مركز الخريطة الأولي
 * @param {number} [props.initialZoom=6] - مستوى التكبير الأولي
 * @param {boolean} [props.showSidebar=true] - إظهار الشريط الجانبي
 * @param {'en'|'ar'} [props.language] - لغة الواجهة (يتم اكتشافها تلقائياً إذا لم تُحدد)
 * @param {Function} [props.onLocationClick] - دالة تُستدعى عند النقر على موقع
 * @param {Function} [props.onAlertClick] - دالة تُستدعى عند النقر على تنبيه
 * @param {Function} [props.onRouteSelect] - دالة تُستدعى عند تحديد طرق
 *
 * @returns {JSX.Element} مكون الخريطة التفاعلية
 *
 * @example
 * ```tsx
 * <InteractiveMapContainer
 *   pageType="location-monitor"
 *   initialCenter={{ lat: 24.7136, lng: 46.6753 }}
 *   onLocationClick={(location) => console.log(location)}
 * />
 * ```
 */
const InteractiveMapContainer: React.FC<InteractiveMapProps> = ({
  locations: externalLocations,
  alerts: externalAlerts,
  routes: externalRoutes,
  statistics: externalStatistics,
  pageType = "default",
  initialCenter = { lat: 24.7136, lng: 46.6753 },
  initialZoom = 6,
  showSidebar = true,
  language: externalLanguage,
  onLocationClick,
  onAlertClick,
  onRouteSelect,
}) => {
  // Auto-detect language from site settings
  const detectedLanguage = useLanguage();
  const language = externalLanguage || detectedLanguage;
  // Load data from JSON files based on page type or use external data
  const {
    locations: dataLocations,
    alerts: dataAlerts,
    routes: dataRoutes,
    statistics: dataStatistics,
    loading,
    error,
  } = usePageData(pageType);

  // Use external data if provided, otherwise use loaded data
  const mapData = useMemo(
    () => ({
      locations: externalLocations || dataLocations,
      alerts: externalAlerts || dataAlerts,
      routes: externalRoutes || dataRoutes,
      statistics: externalStatistics || dataStatistics,
    }),
    [
      externalLocations,
      dataLocations,
      externalAlerts,
      dataAlerts,
      externalRoutes,
      dataRoutes,
      externalStatistics,
      dataStatistics,
    ]
  );

  // Sidebar state management
  const {
    isOpen: sidebarOpen,
    activeTab,
    setIsOpen: setSidebarOpen,
    setActiveTab,
  } = useSidebarState();

  // Handle loading state
  if (loading) {
    return <MapLoadingScreen language={language} />;
  }

  // Handle error state
  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center shadow-lg">
          <div className="text-red-500 mb-4">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {language === "ar" ? "خطأ في تحميل البيانات" : "Error Loading Data"}
          </h3>
          <p className="text-gray-500 text-sm mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            {language === "ar" ? "إعادة المحاولة" : "Retry"}
          </button>
        </div>
      </div>
    );
  }

  const handleResetView = () => {
    console.log("Reset view clicked");
  };

  const handleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  return (
    <div className="relative w-full h-full">
      {/* Google Map View */}
      <GoogleMapView
        locations={mapData.locations}
        center={initialCenter}
        zoom={initialZoom}
        onLocationClick={onLocationClick}
        language={language}
      />

      {/* Map Control Buttons */}
      <MapControlButtons
        sidebarOpen={showSidebar && sidebarOpen}
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        onResetView={handleResetView}
        onFullscreen={handleFullscreen}
        language={language}
      />

      {/* Map Sidebar */}
      {showSidebar && (
        <MapSidebar
          isOpen={sidebarOpen}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onClose={() => setSidebarOpen(false)}
          alerts={mapData.alerts}
          routes={mapData.routes}
          statistics={mapData.statistics}
          language={language}
          onAlertClick={onAlertClick}
          onRouteSelect={onRouteSelect}
        />
      )}
    </div>
  );
};

export default InteractiveMapContainer;
