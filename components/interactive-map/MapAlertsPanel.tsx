'use client';

import React from 'react';
import { Alert } from '../../types/map';

interface MapAlertsPanelProps {
  alerts: Alert[];
  language?: 'en' | 'ar';
  onAlertClick?: (alert: Alert) => void;
}

const MapAlertsPanel: React.FC<MapAlertsPanelProps> = ({
  alerts,
  language = 'en',
  onAlertClick
}) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityLabel = (severity: string) => {
    if (language === 'ar') {
      switch (severity) {
        case 'high': return 'عالي';
        case 'medium': return 'متوسط';
        case 'low': return 'منخفض';
        default: return severity;
      }
    }
    return severity;
  };

  return (
    <div className="p-4">
      <h3 className="font-semibold mb-4 text-gray-800">
        {language === 'ar' ? 'التنبيهات الأخيرة' : 'Recent Alerts'}
      </h3>
      
      {alerts.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>{language === 'ar' ? 'لا توجد تنبيهات' : 'No alerts available'}</p>
        </div>
      ) : (
        <div className="space-y-3">
          {alerts.map((alert) => (
            <div
              key={alert.id}
              className={`border rounded-lg p-3 shadow-sm cursor-pointer transition-colors hover:bg-gray-50 ${
                alert.isRead ? 'bg-white' : 'bg-blue-50 border-blue-200'
              }`}
              onClick={() => onAlertClick?.(alert)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-medium text-sm">
                      {language === 'ar' ? alert.titleAr : alert.title}
                    </h4>
                    <span className={`px-2 py-1 rounded-full text-xs ${getSeverityColor(alert.severity)}`}>
                      {getSeverityLabel(alert.severity)}
                    </span>
                    {!alert.isRead && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 mb-2">
                    {language === 'ar' ? alert.descriptionAr : alert.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{language === 'ar' ? alert.timeAr : alert.time}</span>
                    <span className="text-blue-600">#{alert.vehicleId}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MapAlertsPanel;
