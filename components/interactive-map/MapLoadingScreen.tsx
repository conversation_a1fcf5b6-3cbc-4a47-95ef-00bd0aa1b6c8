'use client';

import React from 'react';

interface MapLoadingScreenProps {
  message?: string;
  messageAr?: string;
  language?: 'en' | 'ar';
}

const MapLoadingScreen: React.FC<MapLoadingScreenProps> = ({
  message = 'Loading Map',
  messageAr = 'جاري تحميل الخريطة',
  language = 'en'
}) => {
  const displayMessage = language === 'ar' ? messageAr : message;
  const loadingText = language === 'ar' ? 'جاري تحميل خرائط جوجل...' : 'Loading Google Maps...';

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
      <div className="bg-white rounded-lg border p-8 text-center shadow-lg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {displayMessage}
        </h3>
        <p className="text-gray-500 text-sm">
          {loadingText}
        </p>
        <div className="mt-4 flex justify-center space-x-1">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
};

export default MapLoadingScreen;
