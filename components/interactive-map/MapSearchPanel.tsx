'use client';

import React from 'react';
import { TripStatistics } from '../../types/map';
import { useSidebarState } from '../../hooks/useSidebarState';
import MapStatisticsCharts from './MapStatisticsCharts';

interface MapSearchPanelProps {
  statistics: TripStatistics;
  language?: 'en' | 'ar';
}

const MapSearchPanel: React.FC<MapSearchPanelProps> = ({
  statistics,
  language = 'en'
}) => {
  const { searchFilters, updateSearchFilters } = useSidebarState();

  const tripCodeOptions = [
    { value: 'Trip Code', label: 'Trip Code', labelAr: 'كود الرحلة' },
    { value: 'Driver Name', label: 'Driver Name', labelAr: 'اسم السائق' },
    { value: 'Vehicle Plate Number', label: 'Vehicle Plate Number', labelAr: 'رقم لوحة المركبة' },
    { value: 'E-locks', label: 'E-locks', labelAr: 'الأقفال الإلكترونية' },
    { value: 'Device Id', label: 'Device Id', labelAr: 'معرف الجهاز' },
    { value: 'Entry Port Name', label: 'Entry Port Name', labelAr: 'اسم منفذ الدخول' },
    { value: 'Exit Port Name', label: 'Exit Port Name', labelAr: 'اسم منفذ الخروج' },
    { value: 'Transit Number', label: 'Transit Number', labelAr: 'رقم العبور' },
    { value: 'Transit Sequence', label: 'Transit Sequence', labelAr: 'تسلسل العبور' },
    { value: 'Transit Date', label: 'Transit Date', labelAr: 'تاريخ العبور' }
  ];

  const handleReset = () => {
    updateSearchFilters({
      showPort: true,
      showCheckpost: true,
      tripCode: 'Trip Code',
      searchValue: ''
    });
  };

  return (
    <div className="p-4 space-y-4">
      {/* Measure Distance Button */}
      <div className="border-b pb-4">
        <button className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
          {language === 'ar' ? 'قياس المسافة' : 'Measure Distance'}
        </button>
      </div>

      {/* Filter Checkboxes */}
      <div className="space-y-3">
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={searchFilters.showPort}
            onChange={(e) => updateSearchFilters({ showPort: e.target.checked })}
            className="rounded text-blue-600"
          />
          <span className="text-sm">
            {language === 'ar' ? 'إظهار الموانئ' : 'Show Port'}
          </span>
        </label>
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={searchFilters.showCheckpost}
            onChange={(e) => updateSearchFilters({ showCheckpost: e.target.checked })}
            className="rounded text-blue-600"
          />
          <span className="text-sm">
            {language === 'ar' ? 'إظهار نقاط التفتيش/مراكز الشرطة' : 'Show CheckPost/Police Station'}
          </span>
        </label>
      </div>

      {/* Trip Code Dropdown */}
      <div>
        <select
          value={searchFilters.tripCode}
          onChange={(e) => updateSearchFilters({ tripCode: e.target.value })}
          className="w-full p-2 border border-gray-300 rounded text-sm"
        >
          {tripCodeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {language === 'ar' ? option.labelAr : option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Search Input */}
      <div>
        <input
          type="text"
          value={searchFilters.searchValue}
          onChange={(e) => updateSearchFilters({ searchValue: e.target.value })}
          placeholder={language === 'ar' ? 'أدخل قيمة البحث...' : 'Enter search value...'}
          className="w-full p-2 border border-gray-300 rounded text-sm"
        />
      </div>

      {/* Reset Button */}
      <div className="pt-2">
        <button
          onClick={handleReset}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors text-sm"
        >
          {language === 'ar' ? 'إعادة تعيين' : 'Reset'}
        </button>
      </div>

      {/* Charts Section */}
      <div className="border-t pt-4 mt-6">
        <h3 className="font-semibold mb-4 text-gray-800">
          {language === 'ar' ? 'إحصائيات الرحلات' : 'Trip Statistics'}
        </h3>
        <MapStatisticsCharts statistics={statistics} language={language} />
      </div>
    </div>
  );
};

export default MapSearchPanel;
