"use client";

import React from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import { PieChartData } from "../../types/map";

interface TripPieChartProps {
  data: PieChartData[];
  language?: "en" | "ar";
}

// Custom tooltip component moved outside
const CustomTooltip = ({ active, payload, language }: any) => {
  if (active && payload?.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-2 border rounded shadow-lg">
        <p className="text-sm font-medium">
          {language === "ar" ? data.nameAr : data.name}
        </p>
        <p className="text-sm text-blue-600">
          {language === "ar" ? "العدد: " : "Count: "}
          {data.value}
        </p>
        <p className="text-sm text-gray-600">
          {language === "ar" ? "النسبة: " : "Percentage: "}
          {data.percentage}%
        </p>
      </div>
    );
  }
  return null;
};

const TripPieChart: React.FC<TripPieChartProps> = ({
  data,
  language = "en",
}) => {
  return (
    <div className="h-64 flex flex-col items-center">
      <div className="relative w-48 h-48">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={80}
              dataKey="value"
              startAngle={90}
              endAngle={450}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${entry.name}-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              content={(props) => (
                <CustomTooltip {...props} language={language} />
              )}
            />
          </PieChart>
        </ResponsiveContainer>

        {/* Center text showing total without alerts */}
        {data.length > 1 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-2xl font-bold text-white bg-orange-500 rounded-full w-16 h-16 flex items-center justify-center">
              {data[1]?.value || 0}
            </span>
          </div>
        )}
      </div>

      {/* Custom Legend */}
      <div className="mt-4 space-y-2 text-sm">
        {data.map((entry, index) => (
          <div
            key={`legend-${entry.name}-${index}`}
            className="flex items-center justify-center space-x-2"
          >
            <div
              className="w-3 h-3"
              style={{ backgroundColor: entry.color }}
            ></div>
            <span>
              {language === "ar" ? entry.nameAr : entry.name} ({entry.value})
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TripPieChart;
