'use client';

import React from 'react';
import { InteractiveMapProps } from '../types/map';
import { useMapData } from '../hooks/useMapData';
import { useSidebarState } from '../hooks/useSidebarState';
import MapContainer from './map/MapContainer';
import MapControls from './map/MapControls';
import Sidebar from './sidebar/Sidebar';
import LoadingSpinner from './map/LoadingSpinner';

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  locations: externalLocations,
  alerts: externalAlerts,
  routes: externalRoutes,
  statistics: externalStatistics,
  initialCenter = { lat: 24.7136, lng: 46.6753 },
  initialZoom = 6,
  showSidebar = true,
  language = 'en',
  onLocationClick,
  onAlertClick,
  onRouteSelect
}) => {
  // Load data from JSON files or use external data
  const {
    locations: dataLocations,
    alerts: dataAlerts,
    routes: dataRoutes,
    statistics: dataStatistics,
    loading,
    error
  } = useMapData();

  // Use external data if provided, otherwise use loaded data
  const locations = externalLocations || dataLocations;
  const alerts = externalAlerts || dataAlerts;
  const routes = externalRoutes || dataRoutes;
  const statistics = externalStatistics || dataStatistics;

  // Sidebar state management
  const {
    isOpen: sidebarOpen,
    activeTab,
    setIsOpen: setSidebarOpen,
    setActiveTab
  } = useSidebarState();

  // Handle loading state
  if (loading) {
    return <LoadingSpinner language={language} />;
  }

  // Handle error state
  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center shadow-lg">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {language === 'ar' ? 'خطأ في تحميل البيانات' : 'Error Loading Data'}
          </h3>
          <p className="text-gray-500 text-sm mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
          </button>
        </div>
      </div>
    );
  }

  const handleMeasureDistance = () => {
    // Implement distance measurement functionality
    console.log('Measure distance clicked');
  };

  const handleResetView = () => {
    // Reset map to initial view
    console.log('Reset view clicked');
  };

  const handleFullscreen = () => {
    // Toggle fullscreen mode
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  return (
    <div className="fixed inset-0 w-full h-full">
      {/* Map Container */}
      <MapContainer
        locations={locations}
        center={initialCenter}
        zoom={initialZoom}
        onLocationClick={onLocationClick}
      />

      {/* Map Controls */}
      <MapControls
        sidebarOpen={showSidebar && sidebarOpen}
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        onMeasureDistance={handleMeasureDistance}
        onResetView={handleResetView}
        onFullscreen={handleFullscreen}
        language={language}
      />

      {/* Sidebar */}
      {showSidebar && (
        <Sidebar
          isOpen={sidebarOpen}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onClose={() => setSidebarOpen(false)}
          alerts={alerts}
          routes={routes}
          statistics={statistics}
          language={language}
          onAlertClick={onAlertClick}
          onRouteSelect={onRouteSelect}
        />
      )}
    </div>
  );
};

export default InteractiveMap;
