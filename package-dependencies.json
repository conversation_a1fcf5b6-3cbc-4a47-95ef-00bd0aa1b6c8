{"name": "interactive-map-dependencies", "description": "Required dependencies for the Interactive Map component", "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "next": "^13.0.0", "typescript": "^5.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/node": "^20.0.0", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "devDependencies": {"@types/google.maps": "^3.54.0", "eslint": "^8.0.0", "eslint-config-next": "^13.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "installation_commands": ["npm install react react-dom next typescript", "npm install @types/react @types/react-dom @types/node", "npm install recharts lucide-react", "npm install tailwindcss autoprefixer postcss", "npm install --save-dev @types/google.maps eslint eslint-config-next"], "environment_variables": {"NEXT_PUBLIC_GOOGLE_MAPS_API_KEY": "Your Google Maps API key here"}, "notes": ["Make sure to get a Google Maps API key from Google Cloud Console", "Enable the Maps JavaScript API in your Google Cloud project", "Add the API key to your .env.local file", "Configure Tailwind CSS in your project", "The component uses TypeScript, so make sure your project supports it"]}