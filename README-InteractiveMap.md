# Interactive Map Component

مكون خريطة تفاعلية شامل ومنظم يمكن إعادة استخدامه في مشاريع مختلفة.

## المميزات

- 🗺️ **خريطة Google Maps تفاعلية** مع علامات مخصصة
- 📊 **رسوم بيانية** لعرض الإحصائيات (Pie Chart & Bar Chart)
- 🔍 **نظام بحث وتصفية** متقدم
- 🚨 **إدارة التنبيهات** مع مستويات خطورة مختلفة
- 🛣️ **إدارة الطرق** مع إمكانية التحديد المتعدد
- 🌐 **دعم اللغة العربية والإنجليزية**
- 📱 **تصميم متجاوب** يعمل على جميع الأجهزة
- 🔧 **قابل للتخصيص** بسهولة

## البنية

```
components/
├── InteractiveMap.tsx          # المكون الرئيسي
├── map/
│   ├── MapContainer.tsx        # حاوي الخريطة
│   ├── MapControls.tsx         # أزرار التحكم
│   └── LoadingSpinner.tsx      # مؤشر التحميل
├── sidebar/
│   ├── Sidebar.tsx             # الشريط الجانبي الرئيسي
│   ├── SidebarTabs.tsx         # تبويبات الشريط الجانبي
│   ├── SearchTab.tsx           # تبويب البحث
│   ├── AlertsTab.tsx           # تبويب التنبيهات
│   └── RoutesTab.tsx           # تبويب الطرق
├── charts/
│   ├── TripCharts.tsx          # مجموعة الرسوم البيانية
│   ├── TripPieChart.tsx        # الرسم البياني الدائري
│   └── TripBarChart.tsx        # الرسم البياني العمودي
└── index.ts                    # ملف التصدير

hooks/
├── useMapData.ts               # إدارة بيانات الخريطة
├── useMapState.ts              # إدارة حالة الخريطة
└── useSidebarState.ts          # إدارة حالة الشريط الجانبي

types/
└── map.ts                      # تعريفات TypeScript

data/
├── locations.json              # بيانات المواقع
├── alerts.json                 # بيانات التنبيهات
├── routes.json                 # بيانات الطرق
└── statistics.json             # بيانات الإحصائيات
```

## الاستخدام الأساسي

```tsx
import { InteractiveMap } from './components';

export default function MyPage() {
  return (
    <div className="w-full h-screen">
      <InteractiveMap />
    </div>
  );
}
```

## الاستخدام المتقدم

```tsx
import { InteractiveMap } from './components';
import { Location, Alert, Route } from './types/map';

export default function MyPage() {
  const handleLocationClick = (location: Location) => {
    console.log('تم النقر على الموقع:', location);
  };

  const handleAlertClick = (alert: Alert) => {
    console.log('تم النقر على التنبيه:', alert);
  };

  const handleRouteSelect = (routes: Route[]) => {
    console.log('تم تحديد الطرق:', routes);
  };

  return (
    <InteractiveMap
      language="ar"
      initialCenter={{ lat: 24.7136, lng: 46.6753 }}
      initialZoom={6}
      showSidebar={true}
      onLocationClick={handleLocationClick}
      onAlertClick={handleAlertClick}
      onRouteSelect={handleRouteSelect}
    />
  );
}
```

## الخصائص (Props)

| الخاصية | النوع | الافتراضي | الوصف |
|---------|------|----------|-------|
| `locations` | `Location[]` | `undefined` | مصفوفة المواقع المخصصة |
| `alerts` | `Alert[]` | `undefined` | مصفوفة التنبيهات المخصصة |
| `routes` | `Route[]` | `undefined` | مصفوفة الطرق المخصصة |
| `statistics` | `TripStatistics` | `undefined` | إحصائيات مخصصة |
| `initialCenter` | `MapCenter` | `{ lat: 24.7136, lng: 46.6753 }` | مركز الخريطة الأولي |
| `initialZoom` | `number` | `6` | مستوى التكبير الأولي |
| `showSidebar` | `boolean` | `true` | إظهار الشريط الجانبي |
| `language` | `'en' \| 'ar'` | `'en'` | لغة الواجهة |
| `onLocationClick` | `(location: Location) => void` | `undefined` | دالة عند النقر على موقع |
| `onAlertClick` | `(alert: Alert) => void` | `undefined` | دالة عند النقر على تنبيه |
| `onRouteSelect` | `(routes: Route[]) => void` | `undefined` | دالة عند تحديد طرق |

## استخدام البيانات المخصصة

```tsx
const customLocations: Location[] = [
  {
    id: 1,
    name: 'موقع مخصص',
    nameAr: 'موقع مخصص',
    lat: 24.7136,
    lng: 46.6753,
    type: 'airport',
    status: 'active'
  }
];

<InteractiveMap locations={customLocations} />
```

## التخصيص

### إضافة أنواع مواقع جديدة

```tsx
// في ملف types/map.ts
export type LocationType = 
  | 'airport' 
  | 'seaport' 
  | 'landport' 
  | 'checkpoint' 
  | 'police_station'
  | 'custom_type'; // نوع جديد
```

### تخصيص الألوان والأيقونات

```tsx
// في ملف hooks/useMapState.ts
const iconConfigs = {
  // إضافة تكوين جديد
  custom_type: {
    color: '#purple',
    svg: `<svg>...</svg>`
  }
};
```

## المتطلبات

- React 18+
- Next.js 13+
- TypeScript
- Tailwind CSS
- Recharts
- Lucide React
- Google Maps API Key

## إعداد Google Maps API

1. احصل على مفتاح API من Google Cloud Console
2. أضف المفتاح إلى ملف `.env.local`:

```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

## الأمثلة

### مثال 1: خريطة بسيطة
```tsx
<InteractiveMap />
```

### مثال 2: خريطة باللغة العربية
```tsx
<InteractiveMap language="ar" />
```

### مثال 3: خريطة بدون شريط جانبي
```tsx
<InteractiveMap showSidebar={false} />
```

### مثال 4: خريطة مع بيانات مخصصة
```tsx
<InteractiveMap 
  locations={myLocations}
  alerts={myAlerts}
  routes={myRoutes}
/>
```

## الدعم والمساهمة

لأي استفسارات أو مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

MIT License
